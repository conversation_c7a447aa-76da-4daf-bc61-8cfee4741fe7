#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转TXT工具配置文件
"""

# 多线程配置
PDF2TXT_CONFIG = {
    # 线程配置
    'max_workers': 4,  # 最大线程数，PDF处理是CPU密集型任务
    'chunk_size': 10,  # 每批处理的文件数
    
    # 文件配置
    'pdf_dir': './pdf/',  # PDF输入目录
    'txt_dir': './txt/',  # TXT输出目录
    'overwrite_existing': False,  # 是否覆盖已存在文件
    'recursive_scan': False,  # 是否递归扫描子目录
    
    # 处理配置
    'timeout_per_file': 300,  # 单文件处理超时时间（秒）
    'max_file_size': 100 * 1024 * 1024,  # 最大文件大小（100MB）
    'min_text_length': 10,  # 最小文本长度
    
    # 重试配置
    'retry_failed': True,  # 是否重试失败的文件
    'max_retries': 2,  # 最大重试次数
    'retry_delay': 1,  # 重试延时（秒）
    
    # 日志配置
    'log_level': 'INFO',  # 日志级别
    'log_file': 'pdf2txt.log',  # 日志文件
    'console_output': True,  # 是否输出到控制台
    'progress_interval': 10,  # 进度显示间隔
}

# PDF处理配置
PDF_PROCESSING_CONFIG = {
    # 提取方法优先级
    'extraction_methods': ['pdfplumber', 'pypdf2'],  # 提取方法优先级
    
    # pdfplumber配置
    'pdfplumber_config': {
        'use_text_flow': True,  # 使用文本流
        'keep_blank_chars': False,  # 保留空白字符
        'layout_bbox_tolerance': 3,  # 布局边界框容差
        'word_margin': 0.1,  # 单词边距
    },
    
    # PyPDF2配置
    'pypdf2_config': {
        'strict': False,  # 严格模式
        'password': None,  # PDF密码
    },
    
    # 文本清理配置
    'text_cleaning': {
        'remove_extra_whitespace': True,  # 移除多余空白
        'normalize_unicode': True,  # Unicode标准化
        'remove_control_chars': True,  # 移除控制字符
        'preserve_line_breaks': True,  # 保留换行符
    }
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    # 内存管理
    'max_memory_usage': 2048,  # 最大内存使用（MB）
    'gc_interval': 50,  # 垃圾回收间隔（处理文件数）
    
    # 并发控制
    'io_bound_workers': 4,  # IO密集型任务线程数
    'cpu_bound_workers': 2,  # CPU密集型任务线程数
    
    # 缓存配置
    'enable_caching': False,  # 启用缓存
    'cache_size': 100,  # 缓存大小
    'cache_ttl': 3600,  # 缓存生存时间（秒）
}

# 质量控制配置
QUALITY_CONFIG = {
    # 文本质量检查
    'min_text_ratio': 0.1,  # 最小文本比例（相对于文件大小）
    'max_empty_pages': 0.5,  # 最大空页面比例
    'check_encoding': True,  # 检查编码
    
    # 错误处理
    'skip_corrupted': True,  # 跳过损坏的文件
    'skip_encrypted': True,  # 跳过加密的文件
    'skip_large_files': True,  # 跳过过大的文件
    
    # 验证配置
    'verify_output': True,  # 验证输出文件
    'compare_file_sizes': True,  # 比较文件大小
}

# 监控配置
MONITOR_CONFIG = {
    # 进度监控
    'enable_progress_bar': True,  # 启用进度条
    'progress_update_interval': 5,  # 进度更新间隔（秒）
    'show_eta': True,  # 显示预计完成时间
    
    # 统计信息
    'enable_statistics': True,  # 启用统计
    'save_statistics': True,  # 保存统计到文件
    'statistics_file': 'pdf2txt_stats.json',  # 统计文件
    
    # 性能监控
    'monitor_memory': True,  # 监控内存使用
    'monitor_cpu': True,  # 监控CPU使用
    'alert_threshold': 0.9,  # 资源使用警告阈值
}

# 输出格式配置
OUTPUT_CONFIG = {
    # 文本格式
    'encoding': 'utf-8',  # 输出编码
    'line_ending': '\n',  # 行结束符
    'add_metadata': False,  # 添加元数据
    
    # 文件命名
    'preserve_structure': True,  # 保持目录结构
    'add_timestamp': False,  # 添加时间戳
    'filename_template': '{basename}.txt',  # 文件名模板
    
    # 压缩选项
    'compress_output': False,  # 压缩输出
    'compression_format': 'gzip',  # 压缩格式
}
