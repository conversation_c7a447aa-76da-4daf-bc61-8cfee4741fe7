#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试关键词上下文提取功能
"""

import os
import sys
import tempfile
from utils import AnalyzeTextWithContext, extract_keyword_context

def create_test_text():
    """创建测试文本"""
    test_content = """
    这是一个测试文档，用于验证关键词上下文提取功能。
    
    人工智能技术在现代社会中发挥着越来越重要的作用。随着大数据技术的发展，
    人工智能算法变得更加精准和高效。企业通过应用人工智能技术，
    能够提高生产效率，降低运营成本。
    
    在金融领域，人工智能被广泛应用于风险控制、智能投顾等方面。
    银行利用人工智能技术进行信贷审批，可以大大提高审批效率。
    
    云计算作为另一项重要技术，为人工智能的发展提供了强大的计算支持。
    通过云计算平台，企业可以更便捷地部署和使用人工智能服务。
    
    区块链技术虽然与人工智能不同，但两者结合可以创造更多价值。
    区块链的去中心化特性与人工智能的智能化特性相结合，
    为数据安全和隐私保护提供了新的解决方案。
    
    大数据分析是人工智能发展的基础。没有大数据，就没有现代的人工智能。
    企业需要建立完善的大数据收集和处理体系，为人工智能应用提供数据支撑。
    """
    return test_content

def test_context_extraction():
    """测试上下文提取功能"""
    print("🧪 开始测试关键词上下文提取功能...")
    
    # 创建临时测试文件
    test_content = create_test_text()
    
    with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix='.txt', delete=False) as f:
        f.write(test_content)
        temp_file = f.name
    
    try:
        # 定义测试关键词
        keywords = ['人工智能', '大数据', '云计算', '区块链']
        
        print(f"📝 测试文件: {temp_file}")
        print(f"🔍 测试关键词: {keywords}")
        print("-" * 60)
        
        # 执行分析
        stats, contexts = AnalyzeTextWithContext(temp_file, keywords, context_length=100)
        
        # 显示统计结果
        print("📊 统计结果:")
        for keyword, count in stats.items():
            print(f"  {keyword}: {count}次")
        print()
        
        # 显示上下文
        print("📝 关键词上下文:")
        for keyword, keyword_contexts in contexts.items():
            print(f"\n🔤 关键词: {keyword}")
            for i, context_info in enumerate(keyword_contexts, 1):
                print(f"  第{i}次出现 (位置: {context_info['position']}):")
                print(f"    {context_info['context']}")
                print()
        
        # 测试单独的上下文提取函数
        print("🔍 测试单独的上下文提取函数:")
        ai_contexts = extract_keyword_context(test_content, '人工智能', 80)
        print(f"找到 {len(ai_contexts)} 个'人工智能'的上下文片段")
        
        for i, context in enumerate(ai_contexts[:2], 1):  # 只显示前2个
            print(f"  片段{i}: {context['context'][:100]}...")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file)
        except:
            pass

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    # 测试空文件
    with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix='.txt', delete=False) as f:
        f.write("")
        empty_file = f.name
    
    try:
        stats, contexts = AnalyzeTextWithContext(empty_file, ['测试'])
        print(f"空文件测试: 统计={stats}, 上下文={contexts}")
        
        # 测试不存在的关键词
        test_content = "这是一个简单的测试文档。"
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix='.txt', delete=False) as f:
            f.write(test_content)
            test_file = f.name
        
        stats, contexts = AnalyzeTextWithContext(test_file, ['不存在的关键词'])
        print(f"不存在关键词测试: 统计={stats}, 上下文={contexts}")
        
        print("✅ 边界情况测试完成！")
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
    
    finally:
        try:
            os.unlink(empty_file)
            os.unlink(test_file)
        except:
            pass

if __name__ == "__main__":
    print("🚀 关键词上下文提取功能测试")
    print("=" * 60)
    
    test_context_extraction()
    test_edge_cases()
    
    print("\n🎉 所有测试完成！")
