"""
使用Python库将PDF转换为文本文件
多线程版本 - 显著提高转换效率
"""
import os
import sys
import time
import threading
import pdfplumber
from PyPDF2 import PdfReader
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
import logging
import argparse
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf2txt.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)


def extract_text_with_pdfplumber(pdf_path):
    """使用pdfplumber提取PDF文本"""
    try:
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text
    except Exception as e:
        print(f"pdfplumber提取失败: {e}")
        return None


def extract_text_with_pypdf2(pdf_path):
    """使用PyPDF2提取PDF文本（备用方法）"""
    try:
        text = ""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text
    except Exception as e:
        print(f"PyPDF2提取失败: {e}")
        return None


def convert_pdf_to_txt(pdf_path, txt_path):
    """将PDF转换为TXT"""
    print(f"正在处理: {os.path.basename(pdf_path)}")
    
    # 首先尝试pdfplumber
    text = extract_text_with_pdfplumber(pdf_path)
    
    # 如果pdfplumber失败，尝试PyPDF2
    if not text:
        print("  尝试备用方法...")
        text = extract_text_with_pypdf2(pdf_path)
    
    if text:
        try:
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(text)
            print(f"  ✅ 转换成功: {os.path.basename(txt_path)}")
            return True
        except Exception as e:
            print(f"  ❌ 保存失败: {e}")
            return False
    else:
        print(f"  ❌ 无法提取文本")
        return False


def main(argv):
    pdf_dir = './pdf/'
    txt_dir = './txt/'
    
    # 确保txt目录存在
    if not os.path.exists(txt_dir):
        os.makedirs(txt_dir)
    
    # 获取所有PDF文件
    pdf_files = [f for f in os.listdir(pdf_dir) if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("PDF目录中没有找到PDF文件")
        return
    
    print(f"找到 {len(pdf_files)} 个PDF文件，开始转换...")
    
    success_count = 0
    for i, file in enumerate(pdf_files, 1):
        file_prefix = file[:-4]
        pdf_path = os.path.join(pdf_dir, file)
        txt_path = os.path.join(txt_dir, file_prefix + '.txt')
        
        print(f"\n[{i}/{len(pdf_files)}] 处理文件: {file}")
        
        if convert_pdf_to_txt(pdf_path, txt_path):
            success_count += 1
    
    print(f"\n🎉 转换完成！")
    print(f"总文件数: {len(pdf_files)}")
    print(f"成功转换: {success_count}")
    print(f"失败: {len(pdf_files) - success_count}")


if __name__ == '__main__':
    main(sys.argv)
