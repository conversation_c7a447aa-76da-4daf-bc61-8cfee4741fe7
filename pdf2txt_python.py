"""
使用Python库将PDF转换为文本文件
多线程版本 - 显著提高转换效率
"""
import os
import sys
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
import logging
import argparse
from pathlib import Path

# 尝试导入PDF处理库
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False
    print("⚠️ pdfplumber 未安装，请运行: pip install pdfplumber")

try:
    from PyPDF2 import PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    print("⚠️ PyPDF2 未安装，请运行: pip install PyPDF2")

# 检查是否至少有一个PDF处理库可用
if not PDFPLUMBER_AVAILABLE and not PYPDF2_AVAILABLE:
    print("❌ 错误：没有可用的PDF处理库！")
    print("💡 请安装至少一个PDF处理库：")
    print("   pip install pdfplumber")
    print("   或")
    print("   pip install PyPDF2")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf2txt.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)


# 多线程配置
DEFAULT_MAX_WORKERS = 4  # 默认线程数，PDF处理是CPU密集型任务
DEFAULT_CHUNK_SIZE = 10  # 每批处理的文件数

# 全局统计信息
stats = {
    'total_files': 0,
    'processed_files': 0,
    'successful_conversions': 0,
    'failed_conversions': 0,
    'skipped_files': 0
}
stats_lock = threading.Lock()

def extract_text_with_pdfplumber(pdf_path):
    """使用pdfplumber提取PDF文本"""
    if not PDFPLUMBER_AVAILABLE:
        logging.warning("pdfplumber 不可用，跳过此方法")
        return None

    try:
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            for i, page in enumerate(pdf.pages):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                except Exception as e:
                    logging.warning(f"页面 {i+1}/{total_pages} 提取失败: {e}")
                    continue
        return text if text.strip() else None
    except Exception as e:
        logging.error(f"pdfplumber提取失败 {os.path.basename(pdf_path)}: {e}")
        return None


def extract_text_with_pypdf2(pdf_path):
    """使用PyPDF2提取PDF文本（备用方法）"""
    if not PYPDF2_AVAILABLE:
        logging.warning("PyPDF2 不可用，跳过此方法")
        return None

    try:
        text = ""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            total_pages = len(pdf_reader.pages)
            for i, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                except Exception as e:
                    logging.warning(f"页面 {i+1}/{total_pages} 提取失败: {e}")
                    continue
        return text if text.strip() else None
    except Exception as e:
        logging.error(f"PyPDF2提取失败 {os.path.basename(pdf_path)}: {e}")
        return None


def convert_single_pdf(pdf_info):
    """转换单个PDF文件（多线程安全）"""
    pdf_path, txt_path, overwrite = pdf_info
    filename = os.path.basename(pdf_path)

    try:
        # 检查输出文件是否已存在
        if os.path.exists(txt_path) and not overwrite:
            logging.info(f"文件已存在，跳过: {filename}")
            with stats_lock:
                stats['skipped_files'] += 1
            return True

        # 检查PDF文件是否存在和可读
        if not os.path.exists(pdf_path):
            logging.error(f"PDF文件不存在: {filename}")
            with stats_lock:
                stats['failed_conversions'] += 1
            return False

        # 检查文件大小
        file_size = os.path.getsize(pdf_path)
        if file_size == 0:
            logging.warning(f"PDF文件为空: {filename}")
            with stats_lock:
                stats['failed_conversions'] += 1
            return False

        logging.info(f"正在处理: {filename} ({file_size/1024:.1f} KB)")

        # 智能选择可用的PDF处理方法
        text = None
        methods_tried = []

        # 优先使用pdfplumber（通常效果更好）
        if PDFPLUMBER_AVAILABLE:
            text = extract_text_with_pdfplumber(pdf_path)
            methods_tried.append("pdfplumber")

        # 如果pdfplumber失败或不可用，尝试PyPDF2
        if not text and PYPDF2_AVAILABLE:
            logging.info(f"  尝试备用方法: {filename}")
            text = extract_text_with_pypdf2(pdf_path)
            methods_tried.append("PyPDF2")

        # 记录使用的方法
        if text:
            logging.info(f"  使用方法: {methods_tried[-1]}")
        else:
            logging.warning(f"  所有方法都失败了: {', '.join(methods_tried)}")

        if text and text.strip():
            try:
                # 确保输出目录存在
                os.makedirs(os.path.dirname(txt_path), exist_ok=True)

                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text)

                # 验证文件是否成功写入
                if os.path.exists(txt_path) and os.path.getsize(txt_path) > 0:
                    logging.info(f"  ✅ 转换成功: {os.path.basename(txt_path)} ({len(text)} 字符)")
                    with stats_lock:
                        stats['successful_conversions'] += 1
                    return True
                else:
                    logging.error(f"  ❌ 文件写入验证失败: {filename}")
                    with stats_lock:
                        stats['failed_conversions'] += 1
                    return False

            except Exception as e:
                logging.error(f"  ❌ 保存失败 {filename}: {e}")
                with stats_lock:
                    stats['failed_conversions'] += 1
                return False
        else:
            logging.warning(f"  ❌ 无法提取文本: {filename}")
            with stats_lock:
                stats['failed_conversions'] += 1
            return False

    except Exception as e:
        logging.error(f"处理文件异常 {filename}: {e}")
        with stats_lock:
            stats['failed_conversions'] += 1
        return False
    finally:
        with stats_lock:
            stats['processed_files'] += 1

def convert_pdf_to_txt(pdf_path, txt_path, overwrite=False):
    """单文件转换函数（兼容性保持）"""
    return convert_single_pdf((pdf_path, txt_path, overwrite))


def convert_pdfs_multithreaded(pdf_files_info, max_workers=DEFAULT_MAX_WORKERS):
    """多线程批量转换PDF文件"""
    total_files = len(pdf_files_info)

    if total_files == 0:
        logging.warning("没有找到需要转换的PDF文件")
        return

    with stats_lock:
        stats['total_files'] = total_files

    logging.info(f"开始多线程转换，共 {total_files} 个文件，使用 {max_workers} 个线程")

    start_time = time.time()

    # 使用线程池执行转换
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有转换任务
        future_to_pdf = {
            executor.submit(convert_single_pdf, pdf_info): pdf_info[0]
            for pdf_info in pdf_files_info
        }

        # 处理完成的任务
        completed = 0
        for future in as_completed(future_to_pdf):
            pdf_path = future_to_pdf[future]
            completed += 1

            try:
                success = future.result()
                progress = (completed / total_files) * 100

                # 每处理10个文件显示一次进度
                if completed % 10 == 0 or completed == total_files:
                    logging.info(f"进度: {completed}/{total_files} ({progress:.1f}%)")

            except Exception as e:
                logging.error(f"转换任务异常 {os.path.basename(pdf_path)}: {e}")

    end_time = time.time()
    duration = end_time - start_time

    # 显示最终统计
    logging.info("=" * 60)
    logging.info("转换完成统计:")
    logging.info(f"总文件数: {stats['total_files']}")
    logging.info(f"成功转换: {stats['successful_conversions']}")
    logging.info(f"转换失败: {stats['failed_conversions']}")
    logging.info(f"跳过文件: {stats['skipped_files']}")
    logging.info(f"总耗时: {duration:.1f} 秒")

    if stats['total_files'] > 0:
        success_rate = (stats['successful_conversions'] / stats['total_files']) * 100
        avg_time = duration / stats['total_files']
        logging.info(f"成功率: {success_rate:.1f}%")
        logging.info(f"平均处理时间: {avg_time:.2f} 秒/文件")

    logging.info("=" * 60)

def scan_pdf_files(pdf_dir, txt_dir, overwrite=False):
    """扫描PDF文件并准备转换信息"""
    if not os.path.exists(pdf_dir):
        logging.error(f"PDF目录不存在: {pdf_dir}")
        return []

    # 获取所有PDF文件
    pdf_files = []
    for root, dirs, files in os.walk(pdf_dir):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_path = os.path.join(root, file)

                # 构建对应的txt路径，保持目录结构
                rel_path = os.path.relpath(pdf_path, pdf_dir)
                txt_path = os.path.join(txt_dir, rel_path[:-4] + '.txt')

                pdf_files.append((pdf_path, txt_path, overwrite))

    logging.info(f"在 {pdf_dir} 中找到 {len(pdf_files)} 个PDF文件")
    return pdf_files

def main(argv=None):
    """主函数"""
    parser = argparse.ArgumentParser(description='PDF转TXT工具 - 多线程版本')

    parser.add_argument('--pdf-dir', '-p',
                       default='./pdf/',
                       help='PDF文件目录 (默认: ./pdf/)')

    parser.add_argument('--txt-dir', '-t',
                       default='./txt/',
                       help='TXT输出目录 (默认: ./txt/)')

    parser.add_argument('--workers', '-w',
                       type=int,
                       default=DEFAULT_MAX_WORKERS,
                       help=f'线程数量 (默认: {DEFAULT_MAX_WORKERS})')

    parser.add_argument('--overwrite', '-o',
                       action='store_true',
                       help='覆盖已存在的TXT文件')

    parser.add_argument('--single-thread',
                       action='store_true',
                       help='使用单线程模式')

    parser.add_argument('--dry-run',
                       action='store_true',
                       help='试运行，只扫描文件不转换')

    parser.add_argument('--recursive', '-r',
                       action='store_true',
                       help='递归处理子目录')

    # 如果没有提供参数，使用默认值
    if argv is None:
        argv = sys.argv[1:]

    args = parser.parse_args(argv)

    # 显示配置信息
    logging.info("🚀 PDF转TXT工具 - 多线程版本")
    logging.info("=" * 60)
    logging.info(f"📁 PDF目录: {args.pdf_dir}")
    logging.info(f"📁 TXT目录: {args.txt_dir}")
    logging.info(f"🔧 线程数: {1 if args.single_thread else args.workers}")
    logging.info(f"🔄 覆盖模式: {'是' if args.overwrite else '否'}")
    logging.info(f"📂 递归处理: {'是' if args.recursive else '否'}")
    logging.info(f"🧪 试运行: {'是' if args.dry_run else '否'}")
    logging.info("=" * 60)

    # 确保输出目录存在
    os.makedirs(args.txt_dir, exist_ok=True)

    # 扫描PDF文件
    if args.recursive:
        pdf_files_info = scan_pdf_files(args.pdf_dir, args.txt_dir, args.overwrite)
    else:
        # 只处理顶级目录
        pdf_files = [f for f in os.listdir(args.pdf_dir) if f.lower().endswith('.pdf')]
        pdf_files_info = [
            (os.path.join(args.pdf_dir, f),
             os.path.join(args.txt_dir, f[:-4] + '.txt'),
             args.overwrite)
            for f in pdf_files
        ]

    if not pdf_files_info:
        logging.warning("没有找到PDF文件")
        return

    if args.dry_run:
        logging.info("🧪 试运行模式 - 文件列表:")
        for i, (pdf_path, txt_path, _) in enumerate(pdf_files_info[:10], 1):
            logging.info(f"  {i}. {os.path.basename(pdf_path)} -> {os.path.basename(txt_path)}")
        if len(pdf_files_info) > 10:
            logging.info(f"  ... 还有 {len(pdf_files_info) - 10} 个文件")
        return

    # 询问用户确认
    try:
        if args.single_thread:
            confirm = input(f"\n是否开始单线程转换 {len(pdf_files_info)} 个文件？ [Y/n]: ").strip().lower()
        else:
            confirm = input(f"\n是否开始多线程转换 {len(pdf_files_info)} 个文件？({args.workers}线程) [Y/n]: ").strip().lower()

        if confirm not in ['', 'y', 'yes']:
            logging.info("❌ 用户取消操作")
            return
    except (KeyboardInterrupt, EOFError):
        logging.info("\n❌ 用户取消操作")
        return

    # 开始转换
    try:
        if args.single_thread:
            # 单线程模式
            logging.info("使用单线程模式...")
            start_time = time.time()

            with stats_lock:
                stats['total_files'] = len(pdf_files_info)

            for i, pdf_info in enumerate(pdf_files_info, 1):
                logging.info(f"[{i}/{len(pdf_files_info)}] 处理文件: {os.path.basename(pdf_info[0])}")
                convert_single_pdf(pdf_info)

            end_time = time.time()
            logging.info(f"单线程转换完成，耗时: {end_time - start_time:.1f} 秒")
        else:
            # 多线程模式
            convert_pdfs_multithreaded(pdf_files_info, args.workers)

    except KeyboardInterrupt:
        logging.info("\n⚠️ 用户中断操作")
    except Exception as e:
        logging.error(f"❌ 转换过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
