# Spider 多线程爬虫使用说明

## 📋 功能介绍

这是一个增强版的巨潮资讯网年报爬虫，新增了多线程下载功能，可以显著提高下载效率。

### ✨ 新增特性

- 🚀 **多线程下载**: 支持多线程并发下载，提高效率
- 📊 **实时统计**: 显示下载进度和成功率统计
- 🔄 **智能重试**: 自动重试失败的下载任务
- 📁 **文件管理**: 自动跳过已存在的文件
- 🛡️ **反爬保护**: 内置延时和随机User-Agent
- 📝 **详细日志**: 完整的操作日志记录
- ⚙️ **灵活配置**: 支持多种参数配置

## 🚀 快速开始

### 1. 基本使用

```bash
# 使用默认配置（3线程）
python spider.py

# 或使用启动脚本
python run_spider.py
```

### 2. 自定义配置

```bash
# 指定线程数
python run_spider.py --workers 5

# 指定延时范围
python run_spider.py --delay-min 2 --delay-max 5

# 指定目标年份
python run_spider.py --year 2023

# 使用单线程模式
python run_spider.py --single-thread
```

## ⚙️ 配置参数

### 命令行参数

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--workers` | `-w` | `3` | 线程数量 |
| `--delay-min` | - | `1` | 最小延时（秒） |
| `--delay-max` | - | `3` | 最大延时（秒） |
| `--company-file` | `-c` | `company_id.txt` | 公司列表文件 |
| `--save-path` | `-s` | `./pdf/` | 保存路径 |
| `--single-thread` | - | - | 单线程模式 |
| `--dry-run` | - | - | 试运行模式 |
| `--year` | - | `2024` | 目标年份 |

### 配置文件

编辑 `spider_config.py` 进行高级配置：

```python
SPIDER_CONFIG = {
    'max_workers': 3,           # 最大线程数
    'delay_range': (1, 3),      # 延时范围
    'timeout': 30,              # 超时时间
    'target_year': '2024',      # 目标年份
    'exclude_keywords': ['摘要', '确认意见']  # 排除关键词
}
```

## 📊 使用示例

### 示例1: 基本多线程下载

```bash
python run_spider.py --workers 3
```

### 示例2: 快速模式（更多线程）

```bash
python run_spider.py --workers 5 --delay-min 1 --delay-max 2
```

### 示例3: 保守模式（避免被封）

```bash
python run_spider.py --workers 2 --delay-min 3 --delay-max 6
```

### 示例4: 下载2023年年报

```bash
python run_spider.py --year 2023
```

### 示例5: 单线程模式

```bash
python run_spider.py --single-thread
```

## 📁 文件结构

```
cninfo-spider/
├── spider.py                 # 主爬虫文件（已增强）
├── run_spider.py             # 启动脚本
├── spider_config.py          # 配置文件
├── company_id.txt            # 公司列表
├── pdf/                      # PDF下载目录
├── spider.log                # 运行日志
└── Spider多线程使用说明.md    # 本说明文件
```

## 📈 性能对比

| 模式 | 线程数 | 延时 | 适用场景 | 预估效率 |
|------|--------|------|----------|----------|
| 单线程 | 1 | 1-3秒 | 稳定可靠 | 基准 |
| 保守多线程 | 2 | 3-6秒 | 避免被封 | 1.5x |
| 平衡多线程 | 3 | 1-3秒 | 推荐使用 | 2.5x |
| 快速多线程 | 5 | 1-2秒 | 快速下载 | 3.5x |

## 🔧 高级功能

### 1. 自定义过滤条件

在 `spider_config.py` 中配置：

```python
SPIDER_CONFIG = {
    'target_year': '2024',
    'exclude_keywords': ['摘要', '确认意见', '更正'],
    'include_keywords': ['年度报告']
}
```

### 2. 网络配置

```python
NETWORK_CONFIG = {
    'timeout': 30,
    'retry_times': 3,
    'user_agents': [...]  # 自定义User-Agent列表
}
```

### 3. 监控统计

程序会自动显示：
- 总公司数和处理进度
- 下载任务数和成功率
- 实时处理状态

## 📝 日志分析

查看 `spider.log` 文件：

```bash
# 查看错误日志
grep "ERROR" spider.log

# 查看下载成功
grep "下载成功" spider.log

# 查看统计信息
grep "统计" spider.log
```

## ⚠️ 注意事项

### 1. 线程数设置

- **推荐**: 3-5个线程
- **最大**: 不超过8个线程
- **原因**: 避免对服务器造成过大压力

### 2. 延时设置

- **最小**: 1秒以上
- **推荐**: 1-3秒
- **保守**: 3-6秒

### 3. 反爬虫策略

- 使用随机延时
- 轮换User-Agent
- 避免过于频繁的请求
- 遵守网站使用条款

### 4. 资源管理

- 确保有足够磁盘空间
- 监控网络流量
- 定期清理日志文件

## 🐛 故障排除

### 常见问题

1. **下载失败率高**
   ```
   解决方案: 降低线程数，增加延时
   ```

2. **网络连接超时**
   ```
   解决方案: 检查网络连接，增加超时时间
   ```

3. **被网站封禁**
   ```
   解决方案: 使用单线程模式，增加延时
   ```

4. **文件保存失败**
   ```
   解决方案: 检查磁盘空间和文件权限
   ```

### 性能优化

1. **调整线程数**
   - 根据网络状况调整
   - 监控CPU和内存使用

2. **优化延时**
   - 平衡效率和稳定性
   - 根据服务器响应调整

3. **网络优化**
   - 使用稳定的网络连接
   - 考虑使用代理（如需要）

## 📞 技术支持

如果遇到问题：

1. 查看日志文件 `spider.log`
2. 尝试降低线程数和增加延时
3. 使用单线程模式测试
4. 检查网络连接状态

## 🔄 更新日志

### v2.0.0 (2025-07-03)
- ✅ 新增多线程下载功能
- ✅ 添加实时统计和进度显示
- ✅ 改进错误处理和重试机制
- ✅ 增强日志记录功能
- ✅ 添加配置文件支持
- ✅ 创建启动脚本

### v1.0.0
- ✅ 基础单线程爬虫功能

---

**享受高效的多线程下载体验！** 🎉
