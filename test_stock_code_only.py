#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试只提供股票代码的功能
"""

import os
import tempfile
from batch_data_crawler import BatchDataCrawler

def test_stock_code_parsing():
    """测试股票代码解析功能"""
    print("🧪 测试股票代码解析功能")
    print("=" * 50)
    
    # 创建测试公司列表文件
    test_companies = [
        "# 测试公司列表 - 只有股票代码",
        "300454",
        "000001", 
        "600036",
        "",
        "# 混合格式测试",
        "600519,贵州茅台",
        "000858",
        "002415,海康威视"
    ]
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix='.txt', delete=False) as f:
        f.write('\n'.join(test_companies))
        temp_file = f.name
    
    try:
        # 创建爬取器并测试
        crawler = BatchDataCrawler(max_workers=1, delay_range=(1, 2))
        companies = crawler.load_company_list(temp_file)
        
        print(f"📊 解析结果:")
        print(f"   总数: {len(companies)} 家公司")
        print()
        
        for i, company in enumerate(companies, 1):
            code = company['code']
            name = company['name']
            print(f"   {i}. {code} - {name if name else '(需要自动获取)'}")
        
        print("\n🔍 测试自动获取公司名称:")
        
        # 测试获取orgId和公司名称
        for company in companies[:3]:  # 只测试前3个
            code = company['code']
            print(f"\n   测试 {code}:")
            
            try:
                stock_info = crawler.get_orgid_by_code(code)
                if stock_info:
                    print(f"     ✅ orgId: {stock_info['orgId']}")
                    print(f"     ✅ 公司名称: {stock_info['zwjc']}")
                else:
                    print(f"     ❌ 无法获取信息")
            except Exception as e:
                print(f"     ❌ 获取失败: {e}")
        
        print("\n✅ 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file)
        except:
            pass

def test_file_formats():
    """测试不同文件格式"""
    print("\n🧪 测试不同文件格式支持")
    print("=" * 50)
    
    formats = [
        {
            'name': '纯股票代码格式',
            'content': ['300454', '000001', '600036']
        },
        {
            'name': '混合格式',
            'content': ['300454', '000001,平安银行', '600036']
        },
        {
            'name': '带注释格式',
            'content': [
                '# 科技股',
                '300454',
                '002415',
                '# 银行股', 
                '000001,平安银行',
                '600036'
            ]
        }
    ]
    
    crawler = BatchDataCrawler(max_workers=1, delay_range=(1, 2))
    
    for fmt in formats:
        print(f"\n📋 {fmt['name']}:")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix='.txt', delete=False) as f:
            f.write('\n'.join(fmt['content']))
            temp_file = f.name
        
        try:
            companies = crawler.load_company_list(temp_file)
            print(f"   解析出 {len(companies)} 家公司:")
            
            for company in companies:
                code = company['code']
                name = company['name']
                status = "有名称" if name else "需获取"
                print(f"     {code} - {name if name else '(空)'} [{status}]")
                
        except Exception as e:
            print(f"   ❌ 解析失败: {e}")
        
        finally:
            try:
                os.unlink(temp_file)
            except:
                pass

def create_sample_stock_list():
    """创建示例股票代码列表"""
    print("\n📝 创建示例股票代码列表")
    print("=" * 50)
    
    sample_file = "sample_stock_codes.txt"
    
    sample_codes = [
        "# 示例股票代码列表 - 只需要提供股票代码",
        "# 系统会自动获取公司名称",
        "",
        "# 科技股",
        "300454",  # 深信服
        "002415",  # 海康威视
        "300059",  # 东方财富
        "002230",  # 科大讯飞
        "",
        "# 银行股", 
        "000001",  # 平安银行
        "600036",  # 招商银行
        "601166",  # 兴业银行
        "",
        "# 白酒股",
        "600519",  # 贵州茅台
        "000858",  # 五粮液
        "000568",  # 泸州老窖
        "",
        "# 新能源",
        "300750",  # 宁德时代
        "002594",  # 比亚迪
        "300274",  # 阳光电源
        "",
        "# 医药股",
        "300760",  # 迈瑞医疗
        "600276",  # 恒瑞医药
        "300015",  # 爱尔眼科
    ]
    
    try:
        with open(sample_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(sample_codes))
        
        print(f"✅ 已创建示例文件: {sample_file}")
        print(f"📊 包含 {len([line for line in sample_codes if line and not line.startswith('#') and line.strip()])} 个股票代码")
        print(f"💡 您可以直接使用这个文件进行测试")
        
    except Exception as e:
        print(f"❌ 创建文件失败: {e}")

def main():
    """主函数"""
    print("🚀 股票代码功能测试")
    print("=" * 60)
    
    test_stock_code_parsing()
    test_file_formats()
    create_sample_stock_list()
    
    print("\n🎉 所有测试完成!")
    print("\n💡 使用说明:")
    print("  1. 公司列表文件中只需要提供股票代码，每行一个")
    print("  2. 系统会自动从巨潮资讯网获取公司名称")
    print("  3. 也支持混合格式: 股票代码,公司名称")
    print("  4. 支持注释行（以#开头）")
    print("  5. 空行会被自动忽略")

if __name__ == "__main__":
    main()
