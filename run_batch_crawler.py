#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量爬取器启动脚本
"""

import os
import sys
import argparse
from batch_data_crawler import BatchDataCrawler
from batch_crawler_config import CRAWLER_CONFIG

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量爬取巨潮资讯网年报数据')
    
    parser.add_argument('--companies', '-c', 
                       default=CRAWLER_CONFIG['company_list_file'],
                       help='公司列表文件路径')
    
    parser.add_argument('--workers', '-w', 
                       type=int, 
                       default=CRAWLER_CONFIG['max_workers'],
                       help='线程数量')
    
    parser.add_argument('--start-date', '-s',
                       default=CRAWLER_CONFIG['start_date'],
                       help='开始日期 (YYYY-MM-DD)')
    
    parser.add_argument('--end-date', '-e',
                       default=CRAWLER_CONFIG['end_date'], 
                       help='结束日期 (YYYY-MM-DD)')
    
    parser.add_argument('--delay-min', 
                       type=int,
                       default=CRAWLER_CONFIG['delay_range'][0],
                       help='最小延时（秒）')
    
    parser.add_argument('--delay-max',
                       type=int, 
                       default=CRAWLER_CONFIG['delay_range'][1],
                       help='最大延时（秒）')
    
    parser.add_argument('--resume', '-r',
                       action='store_true',
                       help='从上次中断处继续')
    
    parser.add_argument('--dry-run',
                       action='store_true',
                       help='试运行，不实际下载')
    
    args = parser.parse_args()
    
    print("🚀 批量数据爬取器")
    print("=" * 60)
    print(f"📁 公司列表: {args.companies}")
    print(f"🔧 线程数: {args.workers}")
    print(f"📅 时间范围: {args.start_date} ~ {args.end_date}")
    print(f"⏱️ 延时范围: {args.delay_min}-{args.delay_max}秒")
    print(f"🔄 继续模式: {'是' if args.resume else '否'}")
    print(f"🧪 试运行: {'是' if args.dry_run else '否'}")
    print("=" * 60)
    
    # 检查公司列表文件
    if not os.path.exists(args.companies):
        print(f"❌ 公司列表文件不存在: {args.companies}")
        print("💡 请确保文件存在，或运行程序自动创建示例文件")
        return
    
    # 创建爬取器
    crawler = BatchDataCrawler(
        max_workers=args.workers,
        delay_range=(args.delay_min, args.delay_max)
    )
    
    if args.dry_run:
        print("🧪 试运行模式 - 只检查公司列表")
        companies = crawler.load_company_list(args.companies)
        print(f"📊 共找到 {len(companies)} 家公司")
        for i, company in enumerate(companies[:10], 1):
            print(f"  {i}. {company['code']} - {company['name']}")
        if len(companies) > 10:
            print(f"  ... 还有 {len(companies) - 10} 家公司")
        return
    
    # 确认开始
    try:
        confirm = input("\n是否开始批量爬取？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 用户取消操作")
            return
    except KeyboardInterrupt:
        print("\n❌ 用户取消操作")
        return
    
    # 运行批量爬取
    try:
        crawler.run_batch_crawl(
            company_list_file=args.companies,
            start_date=args.start_date,
            end_date=args.end_date
        )
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        print("💾 进度已保存，可使用 --resume 参数继续")
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
