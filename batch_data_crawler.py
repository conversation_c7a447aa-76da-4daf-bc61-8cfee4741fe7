#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量数据爬取器 - 多线程版本
用于批量爬取300多家公司的年报PDF并转换为TXT，建立本地数据库
"""

import os
import sys
import time
import random
import requests
import threading
import pdfplumber
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from queue import Queue
import json
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class BatchDataCrawler:
    """批量数据爬取器"""
    
    def __init__(self, max_workers=5, delay_range=(1, 3)):
        self.max_workers = max_workers
        self.delay_range = delay_range
        self.session = requests.Session()
        self.lock = threading.Lock()
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 创建目录
        self.create_directories()
        
        # User-Agent列表
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            'batch_data/pdf',
            'batch_data/txt', 
            'batch_data/logs',
            'batch_data/progress'
        ]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def load_company_list(self, file_path='company_list.txt'):
        """加载公司列表"""
        companies = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # 支持格式: 股票代码 或 股票代码,公司名称
                        parts = line.split(',')
                        code = parts[0].strip()
                        name = parts[1].strip() if len(parts) > 1 else ''
                        companies.append({'code': code, 'name': name})
        except FileNotFoundError:
            logging.error(f"公司列表文件 {file_path} 不存在")
            # 创建示例文件
            self.create_sample_company_list(file_path)
        
        return companies
    
    def create_sample_company_list(self, file_path):
        """创建示例公司列表文件"""
        sample_companies = [
            "# 公司列表文件 - 格式: 股票代码,公司名称(可选)",
            "# 示例:",
            "300454,深信服",
            "000001,平安银行", 
            "000002,万科A",
            "600036,招商银行",
            "600519,贵州茅台",
            "000858,五粮液",
            "002415,海康威视",
            "300059,东方财富",
            "# 请在此添加更多公司代码..."
        ]
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(sample_companies))
        
        logging.info(f"已创建示例公司列表文件: {file_path}")
    
    def get_orgid_by_code(self, stock_code):
        """根据股票代码获取orgId"""
        try:
            url = 'http://www.cninfo.com.cn/new/information/topSearch/query'
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Referer': 'http://www.cninfo.com.cn/',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            params = {
                'keyWord': stock_code,
                'maxNum': 10
            }
            
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data and len(data) > 0:
                for item in data:
                    if item.get('code') == stock_code:
                        return {
                            'orgId': item.get('orgId'),
                            'zwjc': item.get('zwjc', ''),
                            'code': stock_code
                        }
            
            return None
            
        except Exception as e:
            logging.error(f"获取 {stock_code} 的orgId失败: {e}")
            return None
    
    def crawl_company_reports(self, company_info, start_date='2020-01-01', end_date='2024-12-31'):
        """爬取单个公司的年报"""
        stock_code = company_info['code']
        company_name = company_info.get('name', '')
        
        try:
            # 添加随机延时
            time.sleep(random.uniform(*self.delay_range))
            
            logging.info(f"开始处理公司: {stock_code} {company_name}")
            
            # 检查是否已经处理过
            if self.is_already_processed(stock_code):
                logging.info(f"公司 {stock_code} 已处理过，跳过")
                with self.lock:
                    self.stats['skipped'] += 1
                return
            
            # 获取orgId
            stock_info = self.get_orgid_by_code(stock_code)
            if not stock_info:
                logging.error(f"无法获取 {stock_code} 的orgId")
                with self.lock:
                    self.stats['failed'] += 1
                return
            
            orgId = stock_info['orgId']
            if not company_name:
                company_name = stock_info['zwjc']
            
            # 查询年报
            reports = self.query_annual_reports(stock_code, orgId, start_date, end_date)
            
            if not reports:
                logging.warning(f"公司 {stock_code} 没有找到年报")
                with self.lock:
                    self.stats['failed'] += 1
                return
            
            # 下载和转换PDF
            success_count = 0
            for report in reports:
                if self.download_and_convert_pdf(report, stock_code, company_name):
                    success_count += 1
            
            # 记录处理状态
            self.mark_as_processed(stock_code, company_name, success_count)
            
            with self.lock:
                if success_count > 0:
                    self.stats['success'] += 1
                else:
                    self.stats['failed'] += 1
            
            logging.info(f"公司 {stock_code} 处理完成，成功下载 {success_count} 个文件")
            
        except Exception as e:
            logging.error(f"处理公司 {stock_code} 时出错: {e}")
            with self.lock:
                self.stats['failed'] += 1
    
    def query_annual_reports(self, stock_code, orgId, start_date, end_date):
        """查询年报列表"""
        try:
            url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Referer': 'http://www.cninfo.com.cn/',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            data = {
                'pageNum': 1,
                'pageSize': 30,
                'tabName': 'fulltext',
                'column': 'szse',
                'stock': f'{stock_code},{orgId}',
                'searchkey': '年度报告',
                'secid': '',
                'plate': 'sz',
                'category': 'category_ndbg_szsh',
                'trade': '',
                'seDate': f'{start_date}~{end_date}',
                'sortName': '',
                'sortType': '',
                'isHLtitle': 'true'
            }
            
            response = self.session.post(url, data=data, headers=headers, timeout=15)
            response.raise_for_status()
            
            result = response.json()
            announcements = result.get('announcements', [])
            
            # 过滤年报
            annual_reports = []
            for announcement in announcements:
                title = announcement.get('announcementTitle', '')
                if '年度报告' in title and 'PDF' in announcement.get('adjunctType', ''):
                    annual_reports.append(announcement)
            
            return annual_reports
            
        except Exception as e:
            logging.error(f"查询 {stock_code} 年报失败: {e}")
            return []
    
    def download_and_convert_pdf(self, report, stock_code, company_name):
        """下载PDF并转换为TXT"""
        try:
            # 构建文件名
            title = report.get('announcementTitle', '')
            pub_date = report.get('announcementTime', '')[:10]  # YYYY-MM-DD
            
            # 清理文件名中的非法字符
            safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).strip()
            safe_company = "".join(c for c in company_name if c.isalnum() or c in (' ', '-', '_')).strip()
            
            filename = f"{stock_code}_{safe_company}_{pub_date}_{safe_title}"
            pdf_path = f"batch_data/pdf/{filename}.pdf"
            txt_path = f"batch_data/txt/{filename}.txt"
            
            # 检查是否已存在
            if os.path.exists(txt_path):
                logging.info(f"文件已存在，跳过: {filename}")
                return True
            
            # 下载PDF
            pdf_url = f"http://static.cninfo.com.cn/{report['adjunctUrl']}"
            
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Referer': 'http://www.cninfo.com.cn/'
            }
            
            response = self.session.get(pdf_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 保存PDF
            with open(pdf_path, 'wb') as f:
                f.write(response.content)
            
            logging.info(f"PDF下载完成: {filename}")
            
            # 转换为TXT
            if self.convert_pdf_to_txt(pdf_path, txt_path):
                logging.info(f"TXT转换完成: {filename}")
                # 删除PDF文件以节省空间（可选）
                # os.remove(pdf_path)
                return True
            else:
                logging.error(f"TXT转换失败: {filename}")
                return False
                
        except Exception as e:
            logging.error(f"下载转换失败 {report.get('announcementTitle', '')}: {e}")
            return False
    
    def convert_pdf_to_txt(self, pdf_path, txt_path):
        """将PDF转换为TXT"""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            
            if text.strip():
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                return True
            else:
                logging.warning(f"PDF无文本内容: {pdf_path}")
                return False
                
        except Exception as e:
            logging.error(f"PDF转换失败 {pdf_path}: {e}")
            return False
    
    def is_already_processed(self, stock_code):
        """检查是否已经处理过"""
        progress_file = f"batch_data/progress/{stock_code}.json"
        return os.path.exists(progress_file)
    
    def mark_as_processed(self, stock_code, company_name, success_count):
        """标记为已处理"""
        progress_file = f"batch_data/progress/{stock_code}.json"
        progress_data = {
            'stock_code': stock_code,
            'company_name': company_name,
            'processed_time': datetime.now().isoformat(),
            'success_count': success_count
        }
        
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
    
    def run_batch_crawl(self, company_list_file='company_list.txt', start_date='2020-01-01', end_date='2024-12-31'):
        """运行批量爬取"""
        logging.info("开始批量数据爬取...")
        
        # 加载公司列表
        companies = self.load_company_list(company_list_file)
        if not companies:
            logging.error("没有找到公司列表")
            return
        
        self.stats['total'] = len(companies)
        logging.info(f"共需处理 {len(companies)} 家公司")
        
        # 使用线程池执行
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_company = {
                executor.submit(self.crawl_company_reports, company, start_date, end_date): company
                for company in companies
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_company):
                company = future_to_company[future]
                try:
                    future.result()
                except Exception as e:
                    logging.error(f"处理公司 {company['code']} 时发生异常: {e}")
                    with self.lock:
                        self.stats['failed'] += 1
                
                # 显示进度
                completed = self.stats['success'] + self.stats['failed'] + self.stats['skipped']
                progress = (completed / self.stats['total']) * 100
                logging.info(f"进度: {completed}/{self.stats['total']} ({progress:.1f}%)")
        
        # 显示最终统计
        end_time = time.time()
        duration = end_time - start_time
        
        logging.info("=" * 60)
        logging.info("批量爬取完成!")
        logging.info(f"总耗时: {duration:.1f} 秒")
        logging.info(f"总公司数: {self.stats['total']}")
        logging.info(f"成功: {self.stats['success']}")
        logging.info(f"失败: {self.stats['failed']}")
        logging.info(f"跳过: {self.stats['skipped']}")
        logging.info(f"成功率: {(self.stats['success'] / self.stats['total'] * 100):.1f}%")
        logging.info("=" * 60)

def main():
    """主函数"""
    print("🚀 批量数据爬取器")
    print("=" * 60)
    
    # 配置参数
    max_workers = 3  # 线程数，建议不要太大以避免被反爬
    delay_range = (2, 5)  # 请求间隔（秒）
    
    # 创建爬取器
    crawler = BatchDataCrawler(max_workers=max_workers, delay_range=delay_range)
    
    # 运行批量爬取
    crawler.run_batch_crawl(
        company_list_file='company_list.txt',
        start_date='2020-01-01',
        end_date='2024-12-31'
    )

if __name__ == "__main__":
    main()
