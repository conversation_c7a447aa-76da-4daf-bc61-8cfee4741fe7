#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spider 多线程配置文件
"""

# 多线程配置
SPIDER_CONFIG = {
    # 线程配置
    'max_workers': 3,  # 最大线程数，建议2-5个
    'delay_range': (1, 3),  # 请求间隔范围（秒）
    
    # 下载配置
    'timeout': 30,  # 下载超时时间（秒）
    'retry_times': 3,  # 重试次数
    'chunk_size': 8192,  # 下载块大小
    
    # 文件配置
    'company_file': 'company_id.txt',  # 公司列表文件
    'save_path': './pdf/',  # 保存路径
    'overwrite_existing': False,  # 是否覆盖已存在文件
    
    # 过滤配置
    'target_year': '2024',  # 目标年份
    'exclude_keywords': ['摘要', '确认意见', '更正', '补充'],  # 排除关键词
    'include_keywords': ['年度报告'],  # 包含关键词
    
    # 日志配置
    'log_level': 'INFO',  # 日志级别
    'log_file': 'spider.log',  # 日志文件
    'console_output': True,  # 是否输出到控制台
}

# 网络配置
NETWORK_CONFIG = {
    # User-Agent 列表
    'user_agents': [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59"
    ],
    
    # 请求头配置
    'headers': {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-HK;q=0.6,zh-TW;q=0.5",
        'Host': 'www.cninfo.com.cn',
        'Origin': 'http://www.cninfo.com.cn',
        'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice',
        'X-Requested-With': 'XMLHttpRequest'
    },
    
    # 代理配置（可选）
    'proxies': {
        'enabled': False,
        'http': '',
        'https': ''
    }
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'use_session': True,  # 使用会话复用
    'connection_pool_size': 10,  # 连接池大小
    'max_retries': 3,  # 最大重试次数
    'backoff_factor': 0.3,  # 退避因子
    'enable_compression': True,  # 启用压缩
}

# 监控配置
MONITOR_CONFIG = {
    'enable_progress_bar': True,  # 启用进度条
    'progress_update_interval': 5,  # 进度更新间隔（秒）
    'enable_statistics': True,  # 启用统计信息
    'save_statistics': True,  # 保存统计信息到文件
}

# 安全配置
SECURITY_CONFIG = {
    'respect_robots_txt': True,  # 遵守robots.txt
    'max_requests_per_minute': 30,  # 每分钟最大请求数
    'enable_rate_limiting': True,  # 启用速率限制
    'blacklist_check': True,  # 检查黑名单
}
