# 批量数据爬取器使用说明

## 📋 功能介绍

这是一个多线程的批量数据爬取器，用于从巨潮资讯网批量下载300多家公司的年报PDF并转换为TXT格式，建立本地数据库。

### ✨ 主要特性

- 🚀 **多线程并发**: 支持多线程同时下载，提高效率
- 📊 **进度跟踪**: 实时显示下载进度和统计信息
- 🔄 **断点续传**: 支持从中断处继续下载
- 📁 **智能管理**: 自动创建目录结构，避免重复下载
- 🛡️ **反爬保护**: 内置延时和User-Agent轮换
- 📝 **详细日志**: 完整的操作日志记录
- ⚙️ **灵活配置**: 支持多种参数配置

## 📦 文件结构

```
cninfo-spider/
├── batch_data_crawler.py          # 主爬取器
├── run_batch_crawler.py           # 启动脚本
├── batch_crawler_config.py        # 配置文件
├── company_list.txt               # 公司列表
├── batch_data/                    # 数据目录
│   ├── pdf/                      # PDF文件
│   ├── txt/                      # TXT文件
│   ├── logs/                     # 日志文件
│   └── progress/                 # 进度文件
└── 批量爬取使用说明.md            # 本说明文件
```

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的依赖：

```bash
pip install requests pdfplumber tqdm
```

### 2. 配置公司列表

编辑 `company_list.txt` 文件，添加要爬取的公司：

```
# 格式: 股票代码,公司名称
000001,平安银行
600036,招商银行
300454,深信服
# 更多公司...
```

### 3. 基本使用

```bash
# 使用默认配置运行
python run_batch_crawler.py

# 指定线程数
python run_batch_crawler.py --workers 5

# 指定时间范围
python run_batch_crawler.py --start-date 2022-01-01 --end-date 2024-12-31

# 试运行（不实际下载）
python run_batch_crawler.py --dry-run
```

## ⚙️ 配置参数

### 命令行参数

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--companies` | `-c` | `company_list.txt` | 公司列表文件 |
| `--workers` | `-w` | `3` | 线程数量 |
| `--start-date` | `-s` | `2020-01-01` | 开始日期 |
| `--end-date` | `-e` | `2024-12-31` | 结束日期 |
| `--delay-min` | - | `2` | 最小延时（秒） |
| `--delay-max` | - | `5` | 最大延时（秒） |
| `--resume` | `-r` | - | 断点续传 |
| `--dry-run` | - | - | 试运行模式 |

### 配置文件

编辑 `batch_crawler_config.py` 进行高级配置：

```python
CRAWLER_CONFIG = {
    'max_workers': 3,           # 最大线程数
    'delay_range': (2, 5),      # 延时范围
    'timeout': 30,              # 超时时间
    'keep_pdf': True,           # 保留PDF文件
    'overwrite_existing': False # 覆盖已存在文件
}
```

## 📊 使用示例

### 示例1: 基本批量下载

```bash
python run_batch_crawler.py --workers 3 --start-date 2023-01-01
```

### 示例2: 快速模式（更多线程）

```bash
python run_batch_crawler.py --workers 5 --delay-min 1 --delay-max 2
```

### 示例3: 保守模式（避免被封）

```bash
python run_batch_crawler.py --workers 2 --delay-min 5 --delay-max 10
```

### 示例4: 断点续传

```bash
python run_batch_crawler.py --resume
```

## 📁 输出结果

### 目录结构

```
batch_data/
├── pdf/                           # PDF文件
│   ├── 000001_平安银行_2023-04-28_年度报告.pdf
│   └── 600036_招商银行_2023-04-29_年度报告.pdf
├── txt/                           # TXT文件
│   ├── 000001_平安银行_2023-04-28_年度报告.txt
│   └── 600036_招商银行_2023-04-29_年度报告.txt
├── progress/                      # 进度文件
│   ├── 000001.json
│   └── 600036.json
└── logs/
    └── batch_crawler.log          # 运行日志
```

### 进度文件格式

```json
{
  "stock_code": "000001",
  "company_name": "平安银行",
  "processed_time": "2025-07-03T21:30:00",
  "success_count": 3
}
```

## 🔧 高级功能

### 1. 自定义过滤条件

在 `batch_crawler_config.py` 中配置：

```python
FILTER_CONFIG = {
    'min_pdf_size': 1024 * 100,    # 最小文件大小
    'max_pdf_size': 1024 * 1024 * 50,  # 最大文件大小
    'exclude_keywords': ['摘要', '更正'],  # 排除关键词
    'target_years': [2022, 2023, 2024]    # 目标年份
}
```

### 2. 监控和通知

```python
MONITOR_CONFIG = {
    'enable_progress_bar': True,
    'save_progress_interval': 10,
    'enable_email_notification': True
}
```

### 3. 数据库存储

```python
DATABASE_CONFIG = {
    'enabled': True,
    'type': 'sqlite',
    'path': 'batch_data/metadata.db'
}
```

## 📈 性能优化

### 1. 线程数设置

- **保守**: 2-3个线程，延时5-10秒
- **平衡**: 3-5个线程，延时2-5秒  
- **激进**: 5-8个线程，延时1-3秒（可能被封）

### 2. 网络优化

```python
PERFORMANCE_CONFIG = {
    'use_session_pool': True,
    'connection_pool_size': 10,
    'enable_compression': True,
    'chunk_size': 8192
}
```

## ⚠️ 注意事项

### 1. 反爬虫策略

- 使用合理的延时间隔（建议2-5秒）
- 不要使用过多线程（建议不超过5个）
- 定期更换User-Agent
- 遵守网站robots.txt规则

### 2. 资源管理

- 确保有足够的磁盘空间（每个PDF约5-20MB）
- 监控网络流量使用
- 定期清理临时文件

### 3. 法律合规

- 仅用于学习和研究目的
- 遵守相关法律法规
- 尊重网站使用条款

## 🐛 故障排除

### 常见问题

1. **网络连接失败**
   ```
   解决方案: 检查网络连接，降低线程数，增加延时
   ```

2. **PDF转换失败**
   ```
   解决方案: 检查PDF文件完整性，更新pdfplumber版本
   ```

3. **内存不足**
   ```
   解决方案: 降低线程数，启用PDF文件清理
   ```

4. **被网站封禁**
   ```
   解决方案: 增加延时，减少线程数，更换IP
   ```

### 日志分析

查看 `batch_crawler.log` 文件：

```bash
# 查看错误日志
grep "ERROR" batch_crawler.log

# 查看进度统计
grep "进度" batch_crawler.log

# 查看最终统计
tail -20 batch_crawler.log
```

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件 `batch_crawler.log`
2. 检查配置文件设置
3. 尝试降低并发数和增加延时
4. 确认网络连接正常

## 🔄 更新日志

### v1.0.0 (2025-07-03)
- ✅ 初始版本发布
- ✅ 多线程并发下载
- ✅ 断点续传功能
- ✅ 配置文件支持
- ✅ 详细日志记录

---

**祝您使用愉快！** 🎉
