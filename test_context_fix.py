#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试上下文修复效果
"""

import tempfile
import pandas as pd
from utils import extract_keyword_context

def test_context_fix():
    """测试上下文修复效果"""
    print("🧪 测试上下文修复效果...")
    
    # 创建测试文本
    test_content = """
    这是一个测试文档，用于验证关键词上下文提取功能的修复效果。
    
    人工智能技术在现代社会中发挥着越来越重要的作用。随着大数据技术的快速发展，
    云计算基础设施的不断完善，以及区块链技术的逐步成熟，人工智能算法变得更加精准和高效。
    企业通过应用人工智能技术，能够显著提高生产效率，大幅降低运营成本，
    并且在激烈的市场竞争中获得更大的优势。这种技术革命正在深刻改变着我们的工作方式和生活方式，
    为社会发展带来了前所未有的机遇和挑战。
    
    在金融科技领域，人工智能被广泛应用于风险控制、智能投顾、反欺诈检测、
    客户服务自动化等多个重要方面。银行和金融机构利用人工智能技术进行信贷审批，
    可以大大提高审批效率，同时有效降低信贷风险。通过先进的机器学习算法分析客户的历史数据和行为模式，
    金融机构能够更准确地评估客户的信用状况，为客户提供更加个性化的金融服务。
    """
    
    # 测试关键词
    keyword = "人工智能"
    
    print(f"🔍 测试关键词: {keyword}")
    print(f"📝 原始文本长度: {len(test_content)} 字符")
    print("-" * 80)
    
    # 提取上下文
    contexts = extract_keyword_context(test_content, keyword, context_length=300)
    
    print(f"📊 找到 {len(contexts)} 个上下文片段:")
    
    for i, context_info in enumerate(contexts, 1):
        print(f"\n🔤 第{i}个片段:")
        print(f"   位置: {context_info['position']}")
        print(f"   原始长度: {context_info['raw_length']} 字符")
        print(f"   处理后长度: {context_info['length']} 字符")
        print(f"   内容: {context_info['context']}")
        print()
    
    # 测试Excel导出
    print("📊 测试Excel导出...")
    test_excel_export(contexts, keyword)

def test_excel_export(contexts, keyword):
    """测试Excel导出"""
    try:
        # 准备数据
        rows = []
        for i, context_info in enumerate(contexts, 1):
            rows.append({
                '文件名': 'test_file.txt',
                '关键词': keyword,
                '出现次序': i,
                '位置': context_info['position'],
                '原始长度': context_info['raw_length'],
                '处理后长度': context_info['length'],
                '上下文片段': context_info['context']
            })
        
        if rows:
            df = pd.DataFrame(rows)
            
            # 保存到临时Excel文件
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
                excel_file = f.name
            
            # 使用openpyxl引擎写入Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='关键词上下文', index=False)
                
                # 设置列宽
                worksheet = writer.sheets['关键词上下文']
                worksheet.column_dimensions['A'].width = 30  # 文件名
                worksheet.column_dimensions['B'].width = 15  # 关键词
                worksheet.column_dimensions['C'].width = 10  # 出现次序
                worksheet.column_dimensions['D'].width = 10  # 位置
                worksheet.column_dimensions['E'].width = 12  # 原始长度
                worksheet.column_dimensions['F'].width = 12  # 处理后长度
                worksheet.column_dimensions['G'].width = 120 # 上下文片段
                
                # 设置文本换行和行高
                from openpyxl.styles import Alignment, Font
                
                # 设置标题行样式
                for cell in worksheet[1]:
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                
                # 设置数据行样式
                for row_num, row in enumerate(worksheet.iter_rows(min_row=2), start=2):
                    worksheet.row_dimensions[row_num].height = 80
                    
                    for col_num, cell in enumerate(row, start=1):
                        if col_num == 7:  # 上下文片段列
                            cell.alignment = Alignment(wrap_text=True, vertical='top', horizontal='left')
                        else:
                            cell.alignment = Alignment(wrap_text=True, vertical='center', horizontal='center')
            
            print(f"✅ Excel文件已保存: {excel_file}")
            print(f"📊 共导出 {len(rows)} 个上下文片段")
            
            # 验证文件内容
            df_read = pd.read_excel(excel_file, engine='openpyxl')
            print(f"📋 验证读取: {len(df_read)} 行数据")
            
            # 显示第一行数据的上下文长度
            if len(df_read) > 0:
                first_context = df_read.iloc[0]['上下文片段']
                print(f"📏 第一个上下文实际长度: {len(str(first_context))} 字符")
                print(f"📝 第一个上下文预览: {str(first_context)[:100]}...")
            
        else:
            print("⚠️ 没有数据可导出")
            
    except Exception as e:
        print(f"❌ Excel导出测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 上下文修复效果测试")
    print("=" * 80)
    
    test_context_fix()
    
    print("\n🎉 测试完成！")
    print("\n💡 修复内容:")
    print("  ✅ 修复了Excel写入器的options参数错误")
    print("  ✅ 增加上下文长度从200到300字符")
    print("  ✅ 优化文本清理，保留更多原始内容")
    print("  ✅ 增加最大长度限制到800字符")
    print("  ✅ 添加原始长度和处理后长度信息")
    print("  ✅ 增加Excel列宽和行高")
    print("  ✅ 改进GUI表格列宽设置")
