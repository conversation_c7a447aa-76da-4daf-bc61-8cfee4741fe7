#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量爬取进度监控脚本
"""

import os
import json
import glob
from datetime import datetime
from collections import defaultdict

def load_company_list(file_path='company_list.txt'):
    """加载公司列表"""
    companies = {}
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(',')
                    code = parts[0].strip()
                    name = parts[1].strip() if len(parts) > 1 else ''
                    companies[code] = name
    except FileNotFoundError:
        print(f"❌ 公司列表文件不存在: {file_path}")
    
    return companies

def analyze_progress():
    """分析爬取进度"""
    print("📊 批量爬取进度分析")
    print("=" * 60)
    
    # 加载公司列表
    companies = load_company_list()
    total_companies = len(companies)
    
    if total_companies == 0:
        print("❌ 没有找到公司列表")
        return
    
    print(f"📋 总公司数: {total_companies}")
    
    # 分析进度文件
    progress_dir = "batch_data/progress"
    if not os.path.exists(progress_dir):
        print("❌ 进度目录不存在")
        return
    
    progress_files = glob.glob(os.path.join(progress_dir, "*.json"))
    completed_companies = len(progress_files)
    
    print(f"✅ 已完成: {completed_companies}")
    print(f"⏳ 剩余: {total_companies - completed_companies}")
    print(f"📈 完成率: {(completed_companies / total_companies * 100):.1f}%")
    
    # 分析成功率
    success_count = 0
    total_files = 0
    failed_companies = []
    
    for progress_file in progress_files:
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                file_count = data.get('success_count', 0)
                total_files += file_count
                if file_count > 0:
                    success_count += 1
                else:
                    failed_companies.append(data.get('stock_code', ''))
        except Exception as e:
            print(f"⚠️ 读取进度文件失败: {progress_file} - {e}")
    
    print(f"📁 总下载文件数: {total_files}")
    print(f"🎯 成功公司数: {success_count}")
    print(f"❌ 失败公司数: {len(failed_companies)}")
    
    if failed_companies:
        print(f"\n❌ 失败的公司:")
        for code in failed_companies[:10]:  # 只显示前10个
            company_name = companies.get(code, '未知')
            print(f"   {code} - {company_name}")
        if len(failed_companies) > 10:
            print(f"   ... 还有 {len(failed_companies) - 10} 个")
    
    # 分析文件分布
    print(f"\n📁 文件分布分析:")
    analyze_file_distribution()
    
    # 分析最近活动
    print(f"\n⏰ 最近活动:")
    analyze_recent_activity(progress_files)

def analyze_file_distribution():
    """分析文件分布"""
    pdf_dir = "batch_data/pdf"
    txt_dir = "batch_data/txt"
    
    pdf_count = len(glob.glob(os.path.join(pdf_dir, "*.pdf"))) if os.path.exists(pdf_dir) else 0
    txt_count = len(glob.glob(os.path.join(txt_dir, "*.txt"))) if os.path.exists(txt_dir) else 0
    
    print(f"   📄 PDF文件: {pdf_count}")
    print(f"   📝 TXT文件: {txt_count}")
    
    if pdf_count > 0 and txt_count > 0:
        conversion_rate = (txt_count / pdf_count * 100) if pdf_count > 0 else 0
        print(f"   🔄 转换率: {conversion_rate:.1f}%")
    
    # 计算文件大小
    if os.path.exists(pdf_dir):
        pdf_size = sum(os.path.getsize(os.path.join(pdf_dir, f)) 
                      for f in os.listdir(pdf_dir) if f.endswith('.pdf'))
        print(f"   💾 PDF总大小: {pdf_size / (1024*1024):.1f} MB")
    
    if os.path.exists(txt_dir):
        txt_size = sum(os.path.getsize(os.path.join(txt_dir, f)) 
                      for f in os.listdir(txt_dir) if f.endswith('.txt'))
        print(f"   💾 TXT总大小: {txt_size / (1024*1024):.1f} MB")

def analyze_recent_activity(progress_files):
    """分析最近活动"""
    recent_activities = []
    
    for progress_file in progress_files:
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                processed_time = data.get('processed_time', '')
                if processed_time:
                    dt = datetime.fromisoformat(processed_time.replace('Z', '+00:00'))
                    recent_activities.append({
                        'time': dt,
                        'company': f"{data.get('stock_code', '')} - {data.get('company_name', '')}",
                        'files': data.get('success_count', 0)
                    })
        except Exception:
            continue
    
    # 按时间排序，显示最近5个
    recent_activities.sort(key=lambda x: x['time'], reverse=True)
    
    for activity in recent_activities[:5]:
        time_str = activity['time'].strftime('%Y-%m-%d %H:%M:%S')
        print(f"   {time_str} - {activity['company']} ({activity['files']} 文件)")

def show_remaining_companies():
    """显示剩余未处理的公司"""
    print("\n📋 剩余未处理的公司:")
    print("-" * 40)
    
    companies = load_company_list()
    progress_dir = "batch_data/progress"
    
    if not os.path.exists(progress_dir):
        print("❌ 进度目录不存在")
        return
    
    # 获取已处理的公司代码
    processed_codes = set()
    for progress_file in glob.glob(os.path.join(progress_dir, "*.json")):
        code = os.path.basename(progress_file)[:-5]  # 去掉.json
        processed_codes.add(code)
    
    # 找出未处理的公司
    remaining = []
    for code, name in companies.items():
        if code not in processed_codes:
            remaining.append((code, name))
    
    print(f"剩余 {len(remaining)} 家公司:")
    for i, (code, name) in enumerate(remaining[:20], 1):  # 只显示前20个
        print(f"  {i:2d}. {code} - {name}")
    
    if len(remaining) > 20:
        print(f"  ... 还有 {len(remaining) - 20} 家公司")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='批量爬取进度监控')
    parser.add_argument('--remaining', '-r', action='store_true', help='显示剩余公司')
    parser.add_argument('--watch', '-w', action='store_true', help='持续监控模式')
    
    args = parser.parse_args()
    
    if args.remaining:
        show_remaining_companies()
    elif args.watch:
        import time
        try:
            while True:
                os.system('cls' if os.name == 'nt' else 'clear')  # 清屏
                analyze_progress()
                print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 按 Ctrl+C 退出")
                time.sleep(30)  # 30秒刷新一次
        except KeyboardInterrupt:
            print("\n👋 监控已停止")
    else:
        analyze_progress()

if __name__ == "__main__":
    main()
