#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试上下文显示功能
"""

import os
import tempfile
import pandas as pd
from utils import AnalyzeTextWithContext

def create_long_test_text():
    """创建包含长上下文的测试文本"""
    test_content = """
    这是一个专门用于测试关键词上下文提取功能的详细文档。我们将在这里测试各种情况下的上下文提取效果。
    
    人工智能技术在现代社会中发挥着越来越重要的作用。随着大数据技术的快速发展和云计算基础设施的不断完善，
    人工智能算法变得更加精准和高效。企业通过应用人工智能技术，能够显著提高生产效率，大幅降低运营成本，
    并且在市场竞争中获得更大的优势。这种技术革命正在改变着我们的工作方式和生活方式。
    
    在金融科技领域，人工智能被广泛应用于风险控制、智能投顾、反欺诈检测等多个方面。
    银行和金融机构利用人工智能技术进行信贷审批，可以大大提高审批效率，同时降低风险。
    通过机器学习算法分析客户的历史数据和行为模式，金融机构能够更准确地评估客户的信用状况。
    
    云计算作为另一项重要的基础技术，为人工智能的发展提供了强大的计算支持和存储能力。
    通过云计算平台，企业可以更便捷地部署和使用人工智能服务，无需投入大量资金购买昂贵的硬件设备。
    云计算的弹性扩展能力使得人工智能应用能够根据实际需求动态调整计算资源。
    
    区块链技术虽然与人工智能在技术原理上有所不同，但两者结合可以创造更多的商业价值。
    区块链的去中心化特性与人工智能的智能化特性相结合，为数据安全和隐私保护提供了全新的解决方案。
    在供应链管理、数字身份认证、智能合约等领域，区块链与人工智能的结合正在产生革命性的影响。
    
    大数据分析是人工智能发展的重要基础。没有高质量的大数据，就没有现代意义上的人工智能。
    企业需要建立完善的大数据收集、存储、处理和分析体系，为人工智能应用提供充足的数据支撑。
    数据的质量和数量直接影响着人工智能模型的性能和准确性。
    
    物联网技术的发展为大数据的收集提供了更多的渠道和可能性。通过各种传感器和智能设备，
    我们能够实时收集来自不同环境和场景的数据，这些数据经过大数据分析处理后，
    可以为人工智能系统提供更加丰富和准确的训练素材。
    """
    return test_content

def test_context_length():
    """测试上下文长度和显示效果"""
    print("🧪 测试上下文长度和显示效果...")
    
    # 创建测试文件
    test_content = create_long_test_text()
    
    with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix='.txt', delete=False) as f:
        f.write(test_content)
        temp_file = f.name
    
    try:
        # 测试不同的上下文长度
        context_lengths = [100, 150, 200, 250]
        keywords = ['人工智能', '大数据', '云计算']
        
        for length in context_lengths:
            print(f"\n📏 测试上下文长度: {length} 字符")
            print("-" * 50)
            
            stats, contexts = AnalyzeTextWithContext(temp_file, keywords, context_length=length)
            
            for keyword in keywords:
                if keyword in contexts:
                    print(f"\n🔤 关键词: {keyword}")
                    for i, context_info in enumerate(contexts[keyword][:2], 1):  # 只显示前2个
                        context_text = context_info['context']
                        actual_length = context_info.get('length', len(context_text))
                        print(f"  第{i}次出现:")
                        print(f"    长度: {actual_length} 字符")
                        print(f"    内容: {context_text[:100]}{'...' if len(context_text) > 100 else ''}")
                        print()
        
        # 测试Excel导出
        print("\n📊 测试Excel导出...")
        test_excel_export(contexts)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            os.unlink(temp_file)
        except:
            pass

def test_excel_export(contexts):
    """测试Excel导出功能"""
    try:
        # 准备数据
        rows = []
        for keyword, keyword_contexts in contexts.items():
            for i, context_info in enumerate(keyword_contexts, 1):
                context_length = context_info.get('length', len(context_info['context']))
                rows.append({
                    '文件名': 'test_file.txt',
                    '关键词': keyword,
                    '出现次序': i,
                    '位置': context_info['position'],
                    '上下文长度': context_length,
                    '上下文片段': context_info['context']
                })
        
        if rows:
            df = pd.DataFrame(rows)
            
            # 保存到临时Excel文件
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
                excel_file = f.name
            
            # 使用openpyxl引擎写入Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl', options={'strings_to_urls': False}) as writer:
                df.to_excel(writer, sheet_name='关键词上下文', index=False)
                
                # 设置列宽
                worksheet = writer.sheets['关键词上下文']
                worksheet.column_dimensions['A'].width = 30  # 文件名
                worksheet.column_dimensions['B'].width = 15  # 关键词
                worksheet.column_dimensions['C'].width = 10  # 出现次序
                worksheet.column_dimensions['D'].width = 10  # 位置
                worksheet.column_dimensions['E'].width = 12  # 上下文长度
                worksheet.column_dimensions['F'].width = 100 # 上下文片段
                
                # 设置文本换行和行高
                from openpyxl.styles import Alignment, Font
                
                # 设置标题行样式
                for cell in worksheet[1]:
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                
                # 设置数据行样式
                for row_num, row in enumerate(worksheet.iter_rows(min_row=2), start=2):
                    worksheet.row_dimensions[row_num].height = 60
                    
                    for col_num, cell in enumerate(row, start=1):
                        if col_num == 6:  # 上下文片段列
                            cell.alignment = Alignment(wrap_text=True, vertical='top', horizontal='left')
                        else:
                            cell.alignment = Alignment(wrap_text=True, vertical='center', horizontal='center')
            
            print(f"✅ Excel文件已保存: {excel_file}")
            print(f"📊 共导出 {len(rows)} 个上下文片段")
            
            # 验证文件大小
            file_size = os.path.getsize(excel_file)
            print(f"📁 文件大小: {file_size} 字节")
            
            # 清理临时文件
            try:
                os.unlink(excel_file)
            except:
                pass
                
        else:
            print("⚠️ 没有数据可导出")
            
    except Exception as e:
        print(f"❌ Excel导出测试失败: {e}")

if __name__ == "__main__":
    print("🚀 上下文显示功能测试")
    print("=" * 60)
    
    test_context_length()
    
    print("\n🎉 测试完成！")
    print("\n💡 改进说明:")
    print("  - 上下文长度从150字符增加到200字符")
    print("  - 添加了上下文长度信息列")
    print("  - 改进了Excel格式，支持长文本显示")
    print("  - 增加了GUI表格的列宽")
    print("  - 优化了文本清理和格式化")
