#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量爬取器配置文件
"""

# 爬取配置
CRAWLER_CONFIG = {
    # 线程配置
    'max_workers': 3,  # 最大线程数，建议2-5个，避免被反爬
    'delay_range': (2, 5),  # 请求间隔范围（秒）
    
    # 时间范围
    'start_date': '2020-01-01',
    'end_date': '2024-12-31',
    
    # 文件路径
    'company_list_file': 'company_list.txt',
    'base_dir': 'batch_data',
    
    # 网络配置
    'timeout': 30,  # 请求超时时间（秒）
    'retry_times': 3,  # 重试次数
    
    # 文件管理
    'keep_pdf': True,  # 是否保留PDF文件
    'overwrite_existing': False,  # 是否覆盖已存在的文件
}

# 搜索关键词配置
SEARCH_KEYWORDS = [
    '年度报告',
    '年报',
    'annual report'
]

# User-Agent列表
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59'
]

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    'file': 'batch_crawler.log'
}

# 数据库配置（可选，用于存储元数据）
DATABASE_CONFIG = {
    'enabled': False,
    'type': 'sqlite',  # sqlite, mysql, postgresql
    'path': 'batch_data/metadata.db',
    'host': 'localhost',
    'port': 3306,
    'username': '',
    'password': '',
    'database': 'cninfo_data'
}

# 监控配置
MONITOR_CONFIG = {
    'enable_progress_bar': True,
    'save_progress_interval': 10,  # 每处理多少个公司保存一次进度
    'enable_email_notification': False,  # 完成后是否发送邮件通知
    'email_config': {
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'username': '',
        'password': '',
        'to_email': ''
    }
}

# 过滤配置
FILTER_CONFIG = {
    # 文件大小过滤（字节）
    'min_pdf_size': 1024 * 100,  # 最小100KB
    'max_pdf_size': 1024 * 1024 * 50,  # 最大50MB
    
    # 标题过滤
    'exclude_keywords': [
        '摘要',
        '更正',
        '补充',
        '修订',
        '取消'
    ],
    
    # 年份过滤
    'target_years': [2020, 2021, 2022, 2023, 2024]
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'use_session_pool': True,  # 使用会话池
    'connection_pool_size': 10,
    'enable_compression': True,  # 启用压缩
    'chunk_size': 8192,  # 下载块大小
    'max_retries': 3,
    'backoff_factor': 0.3
}
